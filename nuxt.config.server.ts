// Nuxt configuration for server-only builds (API deployment)
export default defineNuxtConfig({
  // Server-side rendering only (no client bundle)
  ssr: true,
  
  // Nitro configuration for server deployment
  nitro: {
    preset: 'node-server',
    
    // CORS configuration for separate client/server deployment
    routeRules: {
      '/api/**': {
        cors: true,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    },
    
    // Server-only optimizations
    experimental: {
      wasm: true
    }
  },
  
  // Runtime configuration for server
  runtimeConfig: {
    // Server-side environment variables
    port: process.env.PORT || 3001,
    
    public: {
      // Server mode
      appMode: 'server-only'
    }
  },
  
  // Build only server-side code
  build: {
    // Optimize for server deployment
    ssr: true
  },
  
  // Include only server directory and necessary files
  srcDir: '.',
  serverDir: 'server',
  
  // Exclude client-side files from server build
  ignore: [
    'components/**/*',
    'layouts/**/*',
    'pages/**/*',
    'plugins/**/*',
    'assets/**/*',
    'public/**/*',
    'app.vue'
  ],
  
  // Disable features not needed for API-only server
  css: [],
  modules: [],
  
  // Development configuration
  devtools: { enabled: false }
})
