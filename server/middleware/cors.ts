export default defineEventHandler(async (event) => {
  // Handle CORS for API routes
  if (event.node.req.url?.startsWith('/api/')) {
    // Set CORS headers
    setHeaders(event, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400' // 24 hours
    })
    
    // Handle preflight requests
    if (getMethod(event) === 'OPTIONS') {
      event.node.res.statusCode = 204
      event.node.res.end()
      return
    }
  }
})
