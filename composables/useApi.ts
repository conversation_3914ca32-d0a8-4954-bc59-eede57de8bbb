/**
 * API composable for handling dynamic endpoint resolution
 * Automatically switches between local and remote API endpoints based on configuration
 */

export const useApi = () => {
  const config = useRuntimeConfig()
  
  /**
   * Get the base URL for API calls
   * In monolith mode: uses local Nuxt server API routes
   * In client-only mode: uses configured remote API base URL
   */
  const getApiBaseUrl = (): string => {
    const { apiBaseUrl, appMode } = config.public
    
    // In monolith mode (local development), use local API routes
    if (appMode === 'monolith' || !apiBaseUrl) {
      return '/api'
    }
    
    // In client-only mode, use configured remote API base URL
    return apiBaseUrl.endsWith('/') ? `${apiBaseUrl}api` : `${apiBaseUrl}/api`
  }
  
  /**
   * Make an API call with automatic endpoint resolution
   * @param endpoint - The API endpoint (e.g., '/hello', '/users')
   * @param options - Fetch options (method, body, headers, etc.)
   */
  const apiCall = async <T = any>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> => {
    const baseUrl = getApiBaseUrl()
    const url = endpoint.startsWith('/') ? `${baseUrl}${endpoint}` : `${baseUrl}/${endpoint}`
    
    // Default headers
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }
    
    try {
      const response = await $fetch<T>(url, {
        ...options,
        headers: defaultHeaders
      })
      
      return response
    } catch (error) {
      console.error(`API call failed for ${url}:`, error)
      throw error
    }
  }
  
  /**
   * Convenience methods for common HTTP verbs
   */
  const get = <T = any>(endpoint: string, options: Omit<RequestInit, 'method'> = {}) => 
    apiCall<T>(endpoint, { ...options, method: 'GET' })
  
  const post = <T = any>(endpoint: string, data?: any, options: Omit<RequestInit, 'method' | 'body'> = {}) => 
    apiCall<T>(endpoint, { 
      ...options, 
      method: 'POST', 
      body: data ? JSON.stringify(data) : undefined 
    })
  
  const put = <T = any>(endpoint: string, data?: any, options: Omit<RequestInit, 'method' | 'body'> = {}) => 
    apiCall<T>(endpoint, { 
      ...options, 
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined 
    })
  
  const del = <T = any>(endpoint: string, options: Omit<RequestInit, 'method'> = {}) => 
    apiCall<T>(endpoint, { ...options, method: 'DELETE' })
  
  /**
   * Get current configuration info (useful for debugging)
   */
  const getConfig = () => ({
    apiBaseUrl: getApiBaseUrl(),
    appMode: config.public.appMode,
    configuredBaseUrl: config.public.apiBaseUrl
  })
  
  return {
    // Core methods
    apiCall,
    getApiBaseUrl,
    
    // HTTP verb shortcuts
    get,
    post,
    put,
    delete: del,
    
    // Utility
    getConfig
  }
}

/**
 * Type definitions for better TypeScript support
 */
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  error?: string
  timestamp?: string
}

export interface HelloWorldResponse {
  message: string
  timestamp: string
  method: string
  url: string
}
