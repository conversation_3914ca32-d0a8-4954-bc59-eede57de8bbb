// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  // Runtime configuration for API endpoints
  runtimeConfig: {
    // Private keys (only available on server-side)
    // These can be overridden by environment variables

    // Public keys (exposed to client-side)
    public: {
      // API base URL - defaults to local development
      // Can be overridden with NUXT_PUBLIC_API_BASE_URL environment variable
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || '',

      // App mode: 'monolith' for local dev, 'client-only' for separate deployment
      appMode: process.env.NUXT_PUBLIC_APP_MODE || 'monolith'
    }
  },

  // Nitro configuration for server deployment
  nitro: {
    // Enable CORS for separate client/server deployment
    experimental: {
      wasm: true
    }
  }
})
