{"name": "@your-org/hybrid-nuxt-client", "version": "1.0.0", "description": "A hybrid Nuxt 3 application that can be deployed as separate client and server components", "private": false, "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "composables/**/*", "components/**/*", "layouts/**/*", "pages/**/*", "plugins/**/*", "assets/**/*", "public/**/*", "nuxt.config.ts", "nuxt.config.client.ts", "app.vue", "README.md"], "keywords": ["nuxt", "nuxt3", "vue", "hybrid", "client-server", "npm-package", "api"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/hybrid-nuxt.git"}, "homepage": "https://github.com/your-username/hybrid-nuxt#readme", "bugs": {"url": "https://github.com/your-username/hybrid-nuxt/issues"}, "scripts": {"build": "nuxt build", "build:client": "NUXT_PUBLIC_APP_MODE=client-only nuxt generate", "build:server": "nuxt build --preset=node-server", "dev": "nuxt dev", "dev:client": "NUXT_PUBLIC_APP_MODE=client-only nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "preview:client": "NUXT_PUBLIC_APP_MODE=client-only nuxt preview", "postinstall": "nuxt prepare", "package:client": "npm run build:client && npm pack", "publish:client": "npm run build:client && npm publish"}, "dependencies": {"nuxt": "3.8.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}}