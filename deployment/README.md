# Deployment Guide

This guide explains how to deploy your hybrid Nuxt app in different configurations.

## Deployment Scenarios

### 1. Local Development (Monolith)
```bash
# Copy local environment
cp .env.example .env

# Start development server
npm run dev
```

### 2. Separate Client and Server Deployment

#### Deploy Server (API)
```bash
# Copy server environment
cp .env.server .env

# Build server
npm run build:server

# Deploy to your server platform (Railway, Heroku, etc.)
# The server will run on the configured PORT (default: 3001)
```

#### Deploy Client
```bash
# Copy client environment and update API URL
cp .env.client .env
# Edit .env and set NUXT_PUBLIC_API_BASE_URL to your server URL

# Build client
npm run build:client

# Deploy to static hosting (Vercel, Netlify, etc.)
# Or publish as npm package
npm run publish:client
```

### 3. NPM Package Usage

After publishing the client as an npm package:

```bash
# In another Nuxt 3 project
npm install @your-org/hybrid-nuxt-client

# Set environment variables
echo "NUXT_PUBLIC_API_BASE_URL=https://your-api-server.com" >> .env
echo "NUXT_PUBLIC_APP_MODE=client-only" >> .env
```

Then extend the package in your `nuxt.config.ts`:
```typescript
export default defineNuxtConfig({
  extends: ['@your-org/hybrid-nuxt-client']
})
```

## Platform-Specific Examples

### Vercel (Client) + Railway (Server)

**Server (Railway):**
```bash
# railway.json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm run build:server && npm start",
    "restartPolicyType": "ON_FAILURE"
  }
}
```

**Client (Vercel):**
```bash
# vercel.json
{
  "builds": [
    {
      "src": "nuxt.config.client.ts",
      "use": "@nuxtjs/vercel-builder"
    }
  ]
}
```

### Netlify (Client) + Heroku (Server)

**Server (Heroku):**
```bash
# Procfile
web: npm run build:server && npm start
```

**Client (Netlify):**
```bash
# netlify.toml
[build]
  command = "npm run build:client"
  publish = "dist"

[build.environment]
  NUXT_PUBLIC_APP_MODE = "client-only"
  NUXT_PUBLIC_API_BASE_URL = "https://your-app.herokuapp.com"
```

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `NUXT_PUBLIC_APP_MODE` | App deployment mode | `monolith`, `client-only`, `server-only` |
| `NUXT_PUBLIC_API_BASE_URL` | API server base URL | `https://api.yourdomain.com` |
| `PORT` | Server port | `3001` |
| `NODE_ENV` | Node environment | `production`, `development` |
