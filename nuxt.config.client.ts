// Nuxt configuration for client-only builds (npm package)
export default defineNuxtConfig({
  extends: './nuxt.config.ts',
  
  // Generate static files for client-only deployment
  ssr: false,
  
  // Nitro configuration for client-only build
  nitro: {
    // Disable server-side API routes for client-only build
    preset: 'static',
    prerender: {
      routes: ['/']
    }
  },
  
  // Override runtime config for client-only mode
  runtimeConfig: {
    public: {
      // Force client-only mode
      appMode: 'client-only',
      // API base URL will be set via environment variable
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
    }
  },
  
  // Build configuration for npm package
  build: {
    // Analyze bundle for optimization
    analyze: process.env.ANALYZE === 'true'
  },
  
  // Exclude server directory from client build
  ignore: [
    'server/**/*'
  ]
})
