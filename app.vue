<template>
  <div>
    <NuxtRouteAnnouncer />

    <!-- Demo of the hybrid API usage -->
    <div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
      <h1>Hybrid Nuxt App Demo</h1>

      <!-- Configuration Info -->
      <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
        <h3>Current Configuration</h3>
        <pre>{{ JSON.stringify(apiConfig, null, 2) }}</pre>
      </div>

      <!-- API Demo -->
      <div style="margin-bottom: 2rem;">
        <h3>API Demo</h3>
        <button
          @click="fetchHello"
          :disabled="loading"
          style="background: #00dc82; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;"
        >
          {{ loading ? 'Loading...' : 'Call Hello API' }}
        </button>

        <div v-if="apiResponse" style="margin-top: 1rem; background: #e8f5e8; padding: 1rem; border-radius: 4px;">
          <h4>API Response:</h4>
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>

        <div v-if="error" style="margin-top: 1rem; background: #ffe8e8; padding: 1rem; border-radius: 4px; color: red;">
          <h4>Error:</h4>
          <pre>{{ error }}</pre>
        </div>
      </div>

      <!-- Usage Instructions -->
      <div style="background: #f0f8ff; padding: 1rem; border-radius: 8px;">
        <h3>How it works:</h3>
        <ul>
          <li><strong>Monolith mode:</strong> API calls go to local <code>/api</code> routes</li>
          <li><strong>Client-only mode:</strong> API calls go to configured remote server</li>
          <li>The <code>useApi()</code> composable automatically handles the routing</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { HelloWorldResponse } from '~/composables/useApi'

// Use the API composable
const { get, getConfig } = useApi()

// Reactive state
const loading = ref(false)
const apiResponse = ref<HelloWorldResponse | null>(null)
const error = ref<string | null>(null)

// Get current configuration
const apiConfig = getConfig()

// Function to fetch hello world data
const fetchHello = async () => {
  loading.value = true
  error.value = null
  apiResponse.value = null

  try {
    const response = await get<HelloWorldResponse>('/hello')
    apiResponse.value = response
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error occurred'
  } finally {
    loading.value = false
  }
}
</script>
