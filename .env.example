# Environment configuration examples for hybrid Nuxt app

# ==============================================
# LOCAL DEVELOPMENT (Monolith Mode)
# ==============================================
# Use these settings for local development where client and server run together

# App mode: 'monolith' for local dev, 'client-only' for separate deployment
NUXT_PUBLIC_APP_MODE=monolith

# API base URL (empty for local development - uses /api routes)
NUXT_PUBLIC_API_BASE_URL=

# Server port for local development
PORT=3000

# ==============================================
# CLIENT-ONLY DEPLOYMENT
# ==============================================
# Use these settings when deploying the client bundle separately
# (e.g., to Vercel, Netlify, or serving from CDN)

# NUXT_PUBLIC_APP_MODE=client-only
# NUXT_PUBLIC_API_BASE_URL=https://your-api-server.com

# ==============================================
# SERVER-ONLY DEPLOYMENT
# ==============================================
# Use these settings when deploying only the API server
# (e.g., to Railway, Heroku, or your own server)

# PORT=3001
# NODE_ENV=production

# ==============================================
# PRODUCTION EXAMPLES
# ==============================================

# Example 1: Client deployed to Vercel, Server to Railway
# Client .env:
# NUXT_PUBLIC_APP_MODE=client-only
# NUXT_PUBLIC_API_BASE_URL=https://your-app.railway.app

# Example 2: Client deployed to Netlify, Server to Heroku
# Client .env:
# NUXT_PUBLIC_APP_MODE=client-only
# NUXT_PUBLIC_API_BASE_URL=https://your-app.herokuapp.com

# Example 3: Both deployed to same domain with reverse proxy
# Client .env:
# NUXT_PUBLIC_APP_MODE=client-only
# NUXT_PUBLIC_API_BASE_URL=https://yourdomain.com
