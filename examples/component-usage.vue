<!-- Example component showing how to use the API composable -->
<template>
  <div class="api-example">
    <h2>API Usage Example</h2>
    
    <!-- Simple GET request -->
    <section>
      <h3>Simple GET Request</h3>
      <button @click="fetchData" :disabled="loading">
        {{ loading ? 'Loading...' : 'Fetch Hello World' }}
      </button>
      
      <div v-if="data" class="result">
        <h4>Response:</h4>
        <pre>{{ JSON.stringify(data, null, 2) }}</pre>
      </div>
    </section>
    
    <!-- POST request example -->
    <section>
      <h3>POST Request Example</h3>
      <input 
        v-model="postData.name" 
        placeholder="Enter your name"
        class="input"
      />
      <button @click="sendData" :disabled="posting">
        {{ posting ? 'Sending...' : 'Send Data' }}
      </button>
      
      <div v-if="postResponse" class="result">
        <h4>POST Response:</h4>
        <pre>{{ JSON.stringify(postResponse, null, 2) }}</pre>
      </div>
    </section>
    
    <!-- Error handling -->
    <div v-if="error" class="error">
      <h4>Error:</h4>
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { HelloWorldResponse } from '~/composables/useApi'

// Use the API composable
const { get, post } = useApi()

// Reactive state
const loading = ref(false)
const posting = ref(false)
const data = ref<HelloWorldResponse | null>(null)
const postResponse = ref<any>(null)
const error = ref<string | null>(null)
const postData = ref({ name: '' })

// Fetch data function
const fetchData = async () => {
  loading.value = true
  error.value = null
  
  try {
    data.value = await get<HelloWorldResponse>('/hello')
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to fetch data'
  } finally {
    loading.value = false
  }
}

// Send data function
const sendData = async () => {
  if (!postData.value.name.trim()) {
    error.value = 'Please enter a name'
    return
  }
  
  posting.value = true
  error.value = null
  
  try {
    // This would call a POST endpoint (you'd need to create /api/users.post.ts)
    postResponse.value = await post('/users', postData.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to send data'
  } finally {
    posting.value = false
  }
}
</script>

<style scoped>
.api-example {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.input {
  padding: 0.5rem;
  margin-right: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  background: #00dc82;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.error {
  margin-top: 1rem;
  padding: 1rem;
  background: #ffe6e6;
  border: 1px solid #ff9999;
  border-radius: 4px;
  color: #d00;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
